"use client";
import React, {
  useState,
  useCallback,
  useRef,
  useMemo,
} from "react";
import { useRegisterShortcut } from "@/app/_component/ShortcutManager";
import { useForm, useFieldArray, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "@/components/ui/form";
import FormInput from "@/app/_component/FormInput";
import FormCheckboxGroup from "@/app/_component/FormCheckboxGroup";
import { Button } from "@/components/ui/button";
import {
  MinusCircle,
  PlusCircle,
  Info,
  DollarSign,
  FileText,
  Building2,
  Hash,
} from "lucide-react";
import { formSubmit, getAllData } from "@/lib/helpers";
import {
  clientCustomFields_routes,
  trackSheets_routes,
  legrandMapping_routes,
} from "@/lib/routePath";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { z } from "zod";
import SearchSelect from "@/app/_component/SearchSelect";
import PageInput from "@/app/_component/PageInput";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import LegrandDetailsComponent from "./LegrandDetailsComponent";

const validateFtpPageFormat = (value: string): boolean => {
  if (!value || value.trim() === "") return false;

  const ftpPageRegex = /^(\d+)\s+of\s+(\d+)$/i;
  const match = value.match(ftpPageRegex);

  if (!match) return false;

  const currentPage = parseInt(match[1], 10);
  const totalPages = parseInt(match[2], 10);

  return currentPage > 0 && totalPages > 0 && currentPage <= totalPages;
};

const trackSheetSchema = z.object({
  clientId: z.string().min(1, "Client is required"),
  entries: z.array(
    z.object({
      company: z.string().min(1, "Company is required"),
      division: z.string().optional(),
      invoice: z.string().min(1, "Invoice is required"),
      masterInvoice: z.string().optional(),
      bol: z.string().min(1, "BOL is required"),
      invoiceDate: z.string().min(1, "Invoice date is required"),
      receivedDate: z.string().min(1, "Received date is required"),
      shipmentDate: z.string().min(1, "Shipment date is required"),
      carrierName: z.string().min(1, "Carrier name is required"),
      invoiceStatus: z.string().min(1, "Invoice status is required"),
      manualMatching: z.string().min(1, "Manual matching is required"),
      invoiceType: z.string().min(1, "Invoice type is required"),
      currency: z.string().min(1, "Currency is required"),
      qtyShipped: z.string().min(1, "Quantity shipped is required"),
      weightUnitName: z.string().min(1, "Weight unit is required"),
      quantityBilledText: z.string().optional(),
      invoiceTotal: z.string().min(1, "Invoice total is required"),
      savings: z.string().optional(),
      ftpFileName: z.string().min(1, "FTP File Name is required"),
      ftpPage: z
        .string()
        .min(1, "FTP Page is required")
        .refine(
          (value) => validateFtpPageFormat(value),
          (value) => {
            if (!value || value.trim() === "") {
              return { message: "FTP Page is required" };
            }

            const ftpPageRegex = /^(\d+)\s+of\s+(\d+)$/i;
            const match = value.match(ftpPageRegex);

            if (!match) {
              return { message: "" };
            }

            const currentPage = parseInt(match[1], 10);
            const totalPages = parseInt(match[2], 10);

            if (currentPage <= 0 || totalPages <= 0) {
              return {
                message: "Page numbers must be positive (greater than 0)",
              };
            }

            if (currentPage > totalPages) {
              return {
                message: `Please enter a page number between ${totalPages} and ${currentPage} `,
              };
            }

            return { message: "Invalid page format" };
          }
        ),
      docAvailable: z.array(z.string()).optional().default([]),
      notes: z.string().optional(),
      mistake: z.string().optional(),
      
      legrandAlias: z.string().optional(),
      legrandCompanyName: z.string().optional(),
      legrandAddress: z.string().optional(),
      legrandZipcode: z.string().optional(),
      customFields: z
        .array(
          z.object({
            id: z.string(),
            name: z.string(),
            type: z.string().optional(),
            value: z.string().optional(),
          })
        )
        .default([]),
    })
  ),
});

type CustomField = {
  id: string;
  name: string;
  type?: string;
  value?: string;
};

type FormValues = {
  associateId: string;
  clientId: string;
  entries: Array<{
    company: string;
    division: string;
    invoice: string;
    masterInvoice: string;
    bol: string;
    invoiceDate: string;
    receivedDate: string;
    shipmentDate: string;
    carrierName: string;
    invoiceStatus: string;
    manualMatching: string;
    invoiceType: string;
    currency: string;
    qtyShipped: string;
    weightUnitName: string;
    quantityBilledText: string;
    invoiceTotal: string;
    savings: string;
    ftpFileName: string;
    ftpPage: string;
    docAvailable: string[];
    notes: string;
    mistake: string;
    // LEGRAND specific fields
    legrandAlias?: string;
    legrandCompanyName?: string;
    legrandAddress?: string;
    legrandZipcode?: string;
    customFields?: CustomField[];
  }>;
};

const CreateTrackSheet = ({ client, carrier, associate, userData }: any) => {
  const companyFieldRefs = useRef<(HTMLElement | null)[]>([]);
  const [generatedFilenames, setGeneratedFilenames] = useState<string[]>([]);
  const [filenameValidation, setFilenameValidation] = useState<boolean[]>([]);
  const [missingFields, setMissingFields] = useState<string[][]>([]);
  const [legrandData, setLegrandData] = useState<any[]>([]);
  const [customFieldsRefresh, setCustomFieldsRefresh] = useState<number>(0);

  // Two-step flow state management
  const [showFullForm, setShowFullForm] = useState(false);
  const [initialAssociateId, setInitialAssociateId] = useState("");
  const [initialClientId, setInitialClientId] = useState("");

  // Simple form for associate and client selection
  const selectionForm = useForm({
    defaultValues: {
      associateId: "",
      clientId: "",
    },
  });

  const associateOptions = associate?.map((a: any) => ({
    value: a.id?.toString(),
    label: a.name,
    name: a.name,
  }));

  const carrierOptions = carrier?.map((c: any) => ({
    value: c.id?.toString(),
    label: c.name,
  }));

  const router = useRouter();

  const form = useForm({
    resolver: zodResolver(trackSheetSchema),
    defaultValues: {
      associateId: "",
      clientId: "",
      entries: [
        {
          company: "",
          division: "",
          invoice: "",
          masterInvoice: "",
          bol: "",
          invoiceDate: new Date().toISOString().split("T")[0],
          receivedDate: new Date().toISOString().split("T")[0],
          shipmentDate: new Date().toISOString().split("T")[0],
          carrierName: "",
          invoiceStatus: "",
          manualMatching: "",
          invoiceType: "",
          currency: "",
          qtyShipped: "",
          weightUnitName: "",
          quantityBilledText: "",
          invoiceTotal: "",
          savings: "",
          ftpFileName: "",
          ftpPage: "",
          docAvailable: [],
          notes: "",
          mistake: "",
          // LEGRAND specific fields
          legrandAlias: "",
          legrandCompanyName: "",
          legrandAddress: "",
          legrandZipcode: "",
          customFields: [],
        },
      ],
    },
  });

  // Filter clients based on initial associate selection (for the pre-form selection) - reactive
  const clientOptions = useMemo(() => {
    if (!initialAssociateId) {
      // If no associate is selected, show all clients
      return client?.map((c: any) => ({
        value: c.id?.toString(),
        label: c.client_name,
        name: c.client_name,
      })) || [];
    }

    // Filter clients that belong to the selected associate
    const filteredClients = client?.filter((c: any) =>
      c.associateId?.toString() === initialAssociateId
    ) || [];

    return filteredClients.map((c: any) => ({
      value: c.id?.toString(),
      label: c.client_name,
      name: c.client_name,
    }));
  }, [initialAssociateId, client]);

  const entries = useWatch({ control: form.control, name: "entries" });

  // Function to validate client belongs to selected associate
  const validateClientForAssociate = useCallback((associateId: string, currentClientId: string) => {
    if (associateId && currentClientId) {
      const currentClient = client?.find((c: any) => c.id?.toString() === currentClientId);
      if (currentClient && currentClient.associateId?.toString() !== associateId) {
        // Current client doesn't belong to selected associate, clear it
        form.setValue("clientId", "");
        setInitialClientId("");
        return false;
      }
    }
    return true;
  }, [client, form]);

  // Function to clear entry-specific client data when global client changes
  const clearEntrySpecificClients = useCallback(() => {
    const currentEntries = form.getValues("entries") || [];
    if (currentEntries.length > 0) {
      const hasEntrySpecificClients = currentEntries.some((entry: any) => entry.clientId);
      if (hasEntrySpecificClients) {
        const updatedEntries = currentEntries.map((entry: any) => ({
          ...entry,
          clientId: "", // Clear entry-specific client so they inherit from global
        }));
        form.setValue("entries", updatedEntries);
      }
    }
  }, [form]);

  // Fetch LEGRAND mapping data - optimized with useCallback
  const fetchLegrandData = useCallback(async () => {
    try {
      const response = await getAllData(
        legrandMapping_routes.GET_LEGRAND_MAPPINGS
      );
      if (response && Array.isArray(response)) {
        setLegrandData(response);
      }
    } catch (error) {
    }
  }, []);

  // Initialize LEGRAND data on mount
  useMemo(() => {
    fetchLegrandData();
  }, [fetchLegrandData]);

  // Function to handle LEGRAND data changes from the component
  const handleLegrandDataChange = (
    entryIndex: number,
    businessUnit: string,
    divisionCode: string
  ) => {
    // Auto-populate company field with business unit
    form.setValue(`entries.${entryIndex}.company`, businessUnit);

    // Auto-populate division field only if a specific division code is provided
    // If divisionCode is empty, it means there are multiple divisions and user should choose
    if (divisionCode) {
      form.setValue(`entries.${entryIndex}.division`, divisionCode);
    } else {
      // Clear division field so user can choose from dropdown
      form.setValue(`entries.${entryIndex}.division`, "");
    }
  };

  // Function to fetch custom fields for a specific client
  const fetchCustomFieldsForClient = useCallback(
    async (clientId: string) => {
      if (!clientId) return [];

      try {
        const allCustomFieldsResponse = await getAllData(
          `${clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS}/${clientId}`
        );

        let customFieldsData: any[] = [];
        if (
          allCustomFieldsResponse &&
          allCustomFieldsResponse.custom_fields &&
          allCustomFieldsResponse.custom_fields.length > 0
        ) {
          customFieldsData = allCustomFieldsResponse.custom_fields.map(
            (field: any) => {
              let autoFilledValue = "";

              // Auto-fill values for auto fields
              if (field.type === "AUTO") {
                if (field.autoOption === "DATE") {
                  // Auto-fill with current date in YYYY-MM-DD format
                  autoFilledValue = new Date().toISOString().split("T")[0];
                } else if (field.autoOption === "USERNAME") {
                  // Auto-fill with current user's username
                  autoFilledValue = userData?.username || "";
                }
              }

              return {
                id: field.id,
                name: field.name,
                type: field.type,
                autoOption: field.autoOption,
                value: autoFilledValue,
              };
            }
          );
        }

        return customFieldsData;
      } catch (error) {
        return [];
      }
    },
    [userData]
  );

  // Handle company auto-population - optimized
  const handleCompanyAutoPopulation = useCallback(
    (entryIndex: number, entryClientId: string) => {
      const selectedClient = client?.find((c: any) => c.id?.toString() === entryClientId);
      const entryClientName = selectedClient?.client_name || "";
      const currentEntry = form.getValues(`entries.${entryIndex}`);

      // Handle company auto-population
      if (entryClientName && entryClientName !== "LEGRAND") {
        form.setValue(`entries.${entryIndex}.company`, entryClientName);
      } else if (entryClientName === "LEGRAND") {
        // For LEGRAND, only clear if it's not already set by the LEGRAND component
        const legrandAlias = (currentEntry as any).legrandAlias;
        if (!legrandAlias && currentEntry.company !== "") {
          form.setValue(`entries.${entryIndex}.company`, "");
        }
      } else {
        if (currentEntry.company !== "") {
          form.setValue(`entries.${entryIndex}.company`, "");
        }
      }
    },
    [form, client]
  );

  // Handle custom fields fetching - optimized
  const handleCustomFieldsFetch = useCallback(
    async (entryIndex: number, entryClientId: string) => {
      if (!entryClientId) {
        // Clear custom fields when no client is selected
        const currentCustomFields = form.getValues(
          `entries.${entryIndex}.customFields`
        );
        if (currentCustomFields && currentCustomFields.length > 0) {
          form.setValue(`entries.${entryIndex}.customFields`, []);
        }
        return;
      }

      const currentCustomFields =
        form.getValues(`entries.${entryIndex}.customFields`) || [];

      // Check if any AUTO-USERNAME fields have empty values (need re-fetch with userData)
      const hasEmptyAutoUsernameFields = currentCustomFields.some(
        (field: any) =>
          field.type === "AUTO" &&
          field.autoOption === "USERNAME" &&
          !field.value &&
          userData?.username
      );

      const shouldFetchCustomFields =
        currentCustomFields.length === 0 ||
        (currentCustomFields.length > 0 && !currentCustomFields[0]?.clientId) ||
        currentCustomFields[0]?.clientId !== entryClientId ||
        hasEmptyAutoUsernameFields;

      if (shouldFetchCustomFields) {
        const customFieldsData = await fetchCustomFieldsForClient(
          entryClientId
        );

        // Use form.setValue to update specific entry's custom fields
        const fieldsWithClientId = customFieldsData.map((field: any) => ({
          ...field,
          clientId: entryClientId, // Track which client these fields belong to
        }));

        form.setValue(`entries.${entryIndex}.customFields`, fieldsWithClientId);

        // Set individual field values after a short delay to ensure form is ready
        setTimeout(() => {
          fieldsWithClientId.forEach((field: any, fieldIndex: number) => {
            const fieldPath =
              `entries.${entryIndex}.customFields.${fieldIndex}.value` as any;
            if (field.value) {
              form.setValue(fieldPath, field.value);
            }
          });
          // Force a refresh of the custom fields display
          setCustomFieldsRefresh((prev) => prev + 1);
        }, 100);
      }
    },
    [form, fetchCustomFieldsForClient, userData?.username]
  );

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "entries",
  });

  // Optimize company field refs management with useMemo
  useMemo(() => {
    companyFieldRefs.current = companyFieldRefs.current.slice(0, fields.length);
  }, [fields.length]);

  // Filename generation function
  const generateFilename = useCallback(
    (entryIndex: number, formValues: any) => {
      try {
        const entry = formValues.entries[entryIndex];
        if (!entry)
          return { filename: "", isValid: false, missing: ["Entry data"] };

        const missing: string[] = [];

        // Get associate name from form selection
        const selectedAssociate = associate?.find(
          (a: any) => a.id?.toString() === formValues.associateId
        );
        const associateName = selectedAssociate?.name || "";
        if (!associateName) {
          missing.push("Associate");
        }

        // Get client name from entry-specific client or fallback to global client
        const entryClientId = (entry as any).clientId || formValues.clientId;
        const selectedClient = client?.find(
          (c: any) => c.id?.toString() === entryClientId
        );
        const clientName = selectedClient?.client_name || "";
        if (!clientName) {
          missing.push("Client");
        }

        // Get carrier name from form value and carrier options
        let carrierName = "";
        if (entry.carrierName) {
          const carrierOption = carrier?.find(
            (c: any) => c.id?.toString() === entry.carrierName
          );
          carrierName = carrierOption?.name || "";
        }

        if (!carrierName) {
          missing.push("Carrier");
        }

        // Get dates
        const receivedDate = entry.receivedDate;
        const invoiceDate = entry.invoiceDate;

        // Generate year and month from current date (when tracksheet is created)
        const currentDate = new Date();
        const year = currentDate.getFullYear().toString();
        const month = currentDate
          .toLocaleString("default", { month: "short" })
          .toUpperCase();

        // Validate invoice date is provided (still required for form validation)
        if (!invoiceDate) {
          missing.push("Invoice Date");
        }

        // Generate received date string
        let receivedDateStr = "";
        if (receivedDate) {
          const date = new Date(receivedDate);
          receivedDateStr = date.toISOString().split("T")[0]; // YYYY-MM-DD format
        } else {
          missing.push("Received Date");
        }

        // Generate base filename from FTP File Name
        const ftpFileName = entry.ftpFileName || "";
        const baseFilename = ftpFileName
          ? ftpFileName.endsWith(".pdf")
            ? ftpFileName
            : `${ftpFileName}.pdf`
          : "";
        if (!baseFilename) {
          missing.push("FTP File Name");
        }

        // Check if all required fields are present
        const isValid = missing.length === 0;

        // Generate full filename path
        const filename = isValid
          ? `/${associateName}/${clientName}/CARRIERINVOICES/${carrierName}/${year}/${month}/${receivedDateStr}/${baseFilename}`
          : "";

        return { filename, isValid, missing };
      } catch (error) {
        return {
          filename: "",
          isValid: false,
          missing: ["Error generating filename"],
        };
      }
    },
    [client, carrier, associate]
  );

  // Optimize filename generation with reactive updates
  const updateFilenames = useCallback(() => {
    const formValues = form.getValues();
    const newFilenames: string[] = [];
    const newValidation: boolean[] = [];
    const newMissingFields: string[][] = [];

    if (formValues.entries && Array.isArray(formValues.entries)) {
      formValues.entries.forEach((_, index) => {
        const { filename, isValid, missing } = generateFilename(
          index,
          formValues
        );
        newFilenames[index] = filename;
        newValidation[index] = isValid;
        newMissingFields[index] = missing || [];
      });
    }

    setGeneratedFilenames(newFilenames);
    setFilenameValidation(newValidation);
    setMissingFields(newMissingFields);
  }, [form, generateFilename]);

  // Initialize form data when component mounts - moved to handleInitialSelection

  // Watch specific form fields that affect filename generation
  const watchedFields = useWatch({
    control: form.control,
    name: ["associateId", "clientId", "entries"],
  });

  // Auto-update filenames when relevant fields change
  useMemo(() => {
    if (showFullForm && watchedFields) {
      const timeoutId = setTimeout(() => {
        updateFilenames();
      }, 100);
      return () => clearTimeout(timeoutId);
    }
  }, [watchedFields, updateFilenames, showFullForm]);

  // Handle initial associate and client selection
  const handleInitialSelection = useCallback(
    (associateId?: string, clientId?: string) => {
      const finalAssociateId = associateId || initialAssociateId;
      const finalClientId = clientId || initialClientId;

      if (!finalAssociateId || !finalClientId) {
        toast.error("Please select both Associate and Client to continue");
        return;
      }

      // Set the form values for the first entry
      form.setValue("associateId", finalAssociateId);
      form.setValue("clientId", finalClientId);

      // Trigger client change logic for the first entry and initialize all entries
      setTimeout(() => {
        handleCompanyAutoPopulation(0, finalClientId);
        handleCustomFieldsFetch(0, finalClientId);
        updateFilenames();

        // Initialize custom fields for any existing entries
        const formValues = form.getValues();
        if (formValues.entries && Array.isArray(formValues.entries)) {
          formValues.entries.forEach((entry: any, index: number) => {
            if (index > 0) { // Skip first entry as it's already handled above
              const entryClientId = entry?.clientId || finalClientId;
              if (entryClientId) {
                handleCustomFieldsFetch(index, entryClientId);
              }
            }
          });
        }
      }, 50);

      setShowFullForm(true);
    },
    [
      initialAssociateId,
      initialClientId,
      form,
      handleCompanyAutoPopulation,
      handleCustomFieldsFetch,
      updateFilenames,
    ]
  );

  // Form submission function
  const onSubmit = useCallback(
    async (values: FormValues) => {
      try {
        // Re-validate filenames with current form data before submission
        const currentFormValues = form.getValues();
        const currentValidation: boolean[] = [];
        const currentMissingFields: string[][] = [];
        const currentFilenames: string[] = [];

        if (
          currentFormValues.entries &&
          Array.isArray(currentFormValues.entries)
        ) {
          currentFormValues.entries.forEach((_, index) => {
            const { filename, isValid, missing } = generateFilename(
              index,
              currentFormValues
            );
            currentValidation[index] = isValid;
            currentMissingFields[index] = missing || [];
            currentFilenames[index] = filename;
          });
        }

        const allFilenamesValid = currentValidation.every((isValid) => isValid);

        if (!allFilenamesValid) {
          // Show detailed error message with missing fields
          const invalidEntries = currentValidation
            .map((isValid, index) => ({
              index,
              isValid,
              missing: currentMissingFields[index],
            }))
            .filter((entry) => !entry.isValid);

          const errorDetails = invalidEntries
            .map(
              (entry) => `Entry ${entry.index + 1}: ${entry.missing.join(", ")}`
            )
            .join(" | ");

          toast.error(`Cannot submit: Missing fields - ${errorDetails}`);
          return;
        }
        const entries = values.entries.map((entry, index) => ({
          company: entry.company,
          division: entry.division,
          invoice: entry.invoice,
          masterInvoice: entry.masterInvoice,
          bol: entry.bol,
          invoiceDate: entry.invoiceDate,
          receivedDate: entry.receivedDate,
          shipmentDate: entry.shipmentDate,
          carrierId: entry.carrierName,
          invoiceStatus: entry.invoiceStatus,
          manualMatching: entry.manualMatching,
          invoiceType: entry.invoiceType,
          currency: entry.currency,
          qtyShipped: entry.qtyShipped,
          weightUnitName: entry.weightUnitName,
          quantityBilledText: entry.quantityBilledText,
          invoiceTotal: entry.invoiceTotal,
          savings: entry.savings,
          ftpFileName: entry.ftpFileName,
          ftpPage: entry.ftpPage,
          docAvailable: entry.docAvailable,
          notes: entry.notes,
          mistake: entry.mistake,
          filePath: currentFilenames[index],
          customFields: entry.customFields?.map((cf) => ({
            id: cf.id,
            value: cf.value,
          })),
        }));
        const formData = {
          clientId: initialClientId, // Use the initial client selection
          entries: entries,
        };

        const result = await formSubmit(
          trackSheets_routes.CREATE_TRACK_SHEETS,
          "POST",
          formData
        );

        if (result.success) {
          toast.success("All TrackSheets created successfully");
          form.reset();
          // Keep the associate and client selections, just reset the form to show a new entry
          setTimeout(() => {
            handleInitialSelection(initialAssociateId, initialClientId);
          }, 100);
        } else {
          toast.error(result.message || "Failed to create TrackSheets");
        }
        router.refresh();
      } catch (error) {
        toast.error("An error occurred while creating the TrackSheets");
      }
    },
    [
      form,
      router,
      generateFilename,
      initialClientId,
      initialAssociateId,
      handleInitialSelection,
    ]
  );

  const addNewEntry = useCallback(() => {
    const newIndex = fields.length;
    append({
      clientId: initialClientId, // Pre-populate with initial client selection
      company: "", // Let the client selection auto-populate this
      division: "",
      invoice: "",
      masterInvoice: "",
      bol: "",
      invoiceDate: new Date().toISOString().split("T")[0],
      receivedDate: new Date().toISOString().split("T")[0],
      shipmentDate: new Date().toISOString().split("T")[0],
      carrierName: "",
      invoiceStatus: "",
      manualMatching: "",
      invoiceType: "",
      currency: "",
      qtyShipped: "",
      weightUnitName: "",
      quantityBilledText: "",
      invoiceTotal: "",
      savings: "",
      ftpFileName: "",
      ftpPage: "",
      docAvailable: [],
      notes: "",
      mistake: "",
      // LEGRAND specific fields
      legrandAlias: "",
      legrandCompanyName: "",
      legrandAddress: "",
      legrandZipcode: "",
      customFields: [],
    } as any);

    setTimeout(() => {
      // Auto-populate company and custom fields for the new entry
      handleCompanyAutoPopulation(newIndex, initialClientId);
      handleCustomFieldsFetch(newIndex, initialClientId);

      // Focus on the company field (first non-client field) for new entries
      if (companyFieldRefs.current[newIndex]) {
        const inputElement =
          companyFieldRefs.current[newIndex]?.querySelector("input") ||
          companyFieldRefs.current[newIndex]?.querySelector("button") ||
          companyFieldRefs.current[newIndex]?.querySelector("select");

        if (inputElement) {
          inputElement.focus();
          try {
            inputElement.click();
          } catch (e) {
            // Element click failed, but focus should still work
          }
        }
      }
      // Update filenames after adding new entry
      updateFilenames();
    }, 200);
  }, [
    append,
    fields.length,
    updateFilenames,
    initialClientId,
    handleCompanyAutoPopulation,
    handleCustomFieldsFetch,
  ]);

  // Register shortcuts using the new shortcut system
  useRegisterShortcut({
    id: 'tracksheet-save',
    name: 'Save TrackSheet',
    description: 'Save the current tracksheet form',
    defaultShortcut: 'Ctrl+S',
    category: 'TrackSheet',
    action: () => form.handleSubmit(onSubmit)()
  });

  useRegisterShortcut({
    id: 'tracksheet-add-entry',
    name: 'Add New Entry',
    description: 'Add a new entry to the tracksheet',
    defaultShortcut: 'Shift+Enter',
    category: 'TrackSheet',
    action: addNewEntry
  });

  // Keyboard event handler for form-level shortcuts only
  const handleFormKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.ctrlKey && (e.key === "s" || e.key === "S")) {
      e.preventDefault(); // Prevent browser's default save behavior
      form.handleSubmit(onSubmit)(); // Trigger form submission
    } else if (e.shiftKey && e.key === "Enter") {
      e.preventDefault(); // Prevent default behavior
      addNewEntry(); // Trigger add new entry
    } else if (
      e.key === "Enter" &&
      !e.ctrlKey &&
      !e.shiftKey &&
      !e.altKey
    ) {
      // Only allow Enter key to submit the form if the submit button has focus
      const activeElement = document.activeElement;
      const isSubmitButton = activeElement?.getAttribute("type") === "submit";

      if (isSubmitButton) {
        e.preventDefault();
        form.handleSubmit(onSubmit)(); // Trigger form submission
      }
      // If not on submit button, let the default behavior happen (don't prevent)
    }
  }, [form, onSubmit, addNewEntry]);
  
  const removeEntry = (index: number) => {
    if (fields.length > 1) {
      remove(index);
      // Update filenames after removing entry
      setTimeout(() => updateFilenames(), 100);
    } else {
      toast.error("You must have at least one entry");
    }
  };

  // Function to get filtered division options based on company from LEGRAND mapping table
  const getFilteredDivisionOptions = (company: string, entryIndex?: number) => {
    if (!company || !legrandData.length) {
      return [];
    }

    // If this is for LEGRAND client and we have an entry index, check for context-specific divisions
    if (entryIndex !== undefined) {
      // Get the entry-specific client name
      const formValues = form.getValues();
      const entry = formValues.entries?.[entryIndex] as any;
      const entryClientId =
        entry?.clientId || (entryIndex === 0 ? formValues.clientId : "");
      const entryClientName =
        clientOptions?.find((c: any) => c.value === entryClientId)?.label || "";

      if (entryClientName === "LEGRAND") {
        const currentAlias = form.getValues(
          `entries.${entryIndex}.legrandAlias`
        );

        if (currentAlias) {
          // Find the selected data for this alias
          const selectedData = legrandData.find((data) => {
            const uniqueKey = `${data.customeCode}-${
              data.aliasShippingNames || data.legalName
            }-${data.shippingBillingAddress}`;
            return uniqueKey === currentAlias;
          });

          if (selectedData) {
            // Get the base alias name from the selected data
            const baseAliasName =
              selectedData.aliasShippingNames &&
              selectedData.aliasShippingNames !== "NONE"
                ? selectedData.aliasShippingNames
                : selectedData.legalName;

            // Find all entries with the same base alias name
            const sameAliasEntries = legrandData.filter((data) => {
              const dataAliasName =
                data.aliasShippingNames && data.aliasShippingNames !== "NONE"
                  ? data.aliasShippingNames
                  : data.legalName;
              return dataAliasName === baseAliasName;
            });

            // Check if we have multiple divisions (either multiple entries OR single entry with "/" in customeCode)
            const allDivisions: string[] = [];
            sameAliasEntries.forEach((entry) => {
              if (entry.customeCode) {
                if (entry.customeCode.includes("/")) {
                  // Split by "/" and add each division
                  const splitDivisions = entry.customeCode
                    .split("/")
                    .map((d: string) => d.trim());
                  allDivisions.push(...splitDivisions);
                } else {
                  // Single division
                  allDivisions.push(entry.customeCode);
                }
              }
            });

            const uniqueDivisions = Array.from(
              new Set(allDivisions.filter((code) => code))
            );

            // If we have multiple unique divisions, show only those divisions
            if (uniqueDivisions.length > 1) {
              const contextDivisions = uniqueDivisions.sort().map((code) => ({
                value: code,
                label: code,
              }));

              return contextDivisions;
            } else {
            }
          }
        }
      }
    }

    // Default: Get all unique customeCode values for the specified business unit
    // Also split by "/" to handle cases like "LEQM/SOLA"
    const allDivisions: string[] = [];
    legrandData
      .filter((data) => data.businessUnit === company && data.customeCode)
      .forEach((data) => {
        if (data.customeCode.includes("/")) {
          // Split by "/" and add each division
          const splitDivisions = data.customeCode
            .split("/")
            .map((d: string) => d.trim());
          allDivisions.push(...splitDivisions);
        } else {
          // Single division
          allDivisions.push(data.customeCode);
        }
      });

    const divisions = Array.from(new Set(allDivisions.filter((code) => code)))
      .sort()
      .map((code) => ({
        value: code,
        label: code,
      }));
    return divisions;
  };

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="w-full px-2 py-3">
          {/* Associate and Client Selection Section - Always Visible */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
            <div className="flex items-center space-x-2 mb-4">
              <Building2 className="w-5 h-5 text-blue-600" />
              <h2 className="text-lg font-semibold text-gray-900">
                Create TrackSheet
              </h2>
            </div>

            <Form {...selectionForm}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <SearchSelect
                    form={selectionForm}
                    name="associateId"
                    label="Select Associate"
                    placeholder="Search Associate..."
                    isRequired
                    options={associateOptions || []}
                    onValueChange={(value) => {
                      setInitialAssociateId(value);

                      // Validate current client belongs to new associate
                      if (value && initialClientId) {
                        validateClientForAssociate(value, initialClientId);
                      } else {
                        setInitialClientId(""); // Clear client when associate changes
                        selectionForm.setValue("clientId", ""); // Clear form value too
                      }
                      setShowFullForm(false); // Hide form when associate changes
                    }}
                  />
                </div>

                <div>
                  <SearchSelect
                    form={selectionForm}
                    name="clientId"
                    label="Select Client"
                    placeholder="Search Client..."
                    isRequired
                    disabled={!initialAssociateId}
                    options={clientOptions || []}
                    onValueChange={(value) => {
                      setInitialClientId(value);

                      // Clear entry-specific client data when global client changes
                      if (showFullForm) {
                        clearEntrySpecificClients();
                      }

                      if (value && initialAssociateId) {
                        // Auto-trigger form display when both are selected
                        setTimeout(() => {
                          handleInitialSelection(initialAssociateId, value);
                        }, 100);
                      } else {
                        setShowFullForm(false);
                      }
                    }}
                  />
                </div>
              </div>
            </Form>
          </div>

          {/* Form Section - Only show when both associate and client are selected */}
          {showFullForm && (
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                onKeyDown={handleFormKeyDown}
                className="space-y-3"
              >
                {fields.map((field, index) => (
                  <div key={field.id} className="relative">
                    {/* Entry Header - Ultra Compact */}
                    <div className="flex items-center justify-between mb-2 bg-gray-100 rounded-md px-3 py-2 border border-gray-200">
                      <div className="flex items-center space-x-2">
                        <div className="w-5 h-5 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold text-xs">
                          {index + 1}
                        </div>
                        <h2 className="text-sm font-semibold text-gray-900">
                          Entry #{index + 1}
                        </h2>
                      </div>
                    </div>

                    {/* Main Form Content - Compact Layout */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3">
                      {/* Client Selection Row - For every entry */}
                      <div className="mb-3 pb-3 border-b border-gray-100">
                        {/* Client Selection & Company Information */}
                        <div className="bg-gray-50 rounded-md p-2 mb-3">
                          <div className="flex items-center space-x-2 mb-2">
                            <Building2 className="w-4 h-4 text-blue-600" />
                            <h3 className="text-sm font-semibold text-gray-900">
                              Client Information
                            </h3>
                          </div>
                          {/* Single Row - FTP File Name and FTP Page (Associate and Client already selected) */}
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mb-3">
                            <div className="">
                              <FormInput
                                className="mt-2"
                                form={form}
                                label="FTP File Name"
                                name={`entries.${index}.ftpFileName`}
                                type="text"
                                isRequired
                              />
                            </div>
                            <PageInput
                              className="mt-2"
                              form={form}
                              label="FTP Page"
                              name={`entries.${index}.ftpPage`}
                              isRequired
                            />
                            <div className=""></div>{" "}
                            {/* Empty third column to match Company row layout */}
                          </div>

                          {/* LEGRAND Specific Section - Show when LEGRAND for this specific entry */}
                          {(() => {
                            const formValues = form.getValues();
                            const entry = formValues.entries?.[index] as any;
                            const entryClientId =
                              entry?.clientId ||
                              (index === 0 ? formValues.clientId : "");
                            const entryClientName =
                              clientOptions?.find(
                                (c: any) => c.value === entryClientId
                              )?.label || "";
                            return entryClientName === "LEGRAND";
                          })() && (
                            <div className="mb-3">
                              <LegrandDetailsComponent
                                form={form}
                                entryIndex={index}
                                onLegrandDataChange={handleLegrandDataChange}
                              />
                            </div>
                          )}

                          {/* Third Row - Company, Division, Carrier */}
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                            <div
                              ref={(el) => {
                                companyFieldRefs.current[index] = el;
                              }}
                              className="flex flex-col mb-1 [&_input]:h-10"
                            >
                              <FormInput
                                form={form}
                                label="Company"
                                name={`entries.${index}.company`}
                                type="text"
                                disable={(() => {
                                  const formValues = form.getValues();
                                  const entry = formValues.entries?.[
                                    index
                                  ] as any;
                                  const entryClientId =
                                    entry?.clientId ||
                                    (index === 0 ? formValues.clientId : "");
                                  const entryClientName =
                                    clientOptions?.find(
                                      (c: any) => c.value === entryClientId
                                    )?.label || "";
                                  return entryClientName === "LEGRAND";
                                })()}
                              />
                            </div>
                            <div className="flex flex-col">
                              {(() => {
                                const formValues = form.getValues();
                                const entry = formValues.entries?.[
                                  index
                                ] as any;
                                const entryClientId =
                                  entry?.clientId ||
                                  (index === 0 ? formValues.clientId : "");
                                const entryClientName =
                                  clientOptions?.find(
                                    (c: any) => c.value === entryClientId
                                  )?.label || "";
                                const isLegrand = entryClientName === "LEGRAND";

                                return isLegrand ? (
                                  <SearchSelect
                                    form={form}
                                    name={`entries.${index}.division`}
                                    label="Division"
                                    placeholder="Search Division"
                                    disabled={false}
                                    options={getFilteredDivisionOptions(
                                      entries?.[index]?.company || "",
                                      index
                                    )}
                                  />
                                ) : (
                                  <FormInput
                                    form={form}
                                    className="mt-2"
                                    label="Division"
                                    name={`entries.${index}.division`}
                                    type="text"
                                    placeholder="Enter Division"
                                  />
                                );
                              })()}
                            </div>
                            <div className="flex flex-col">
                              <SearchSelect
                                form={form}
                                name={`entries.${index}.carrierName`}
                                label="Select Carrier"
                                placeholder="Search Carrier"
                                isRequired
                                options={carrierOptions
                                  ?.filter((carrier: any) => {
                                    // Filter out carriers already selected in other entries
                                    const currentEntries =
                                      form.getValues("entries") || [];
                                    const isSelectedInOtherEntries =
                                      currentEntries.some(
                                        (entry: any, entryIndex: number) =>
                                          entryIndex !== index &&
                                          entry.carrierName === carrier.value
                                      );
                                    return !isSelectedInOtherEntries;
                                  }) || []}
                                onValueChange={() => {
                                  // Update filenames when carrier changes
                                  setTimeout(() => updateFilenames(), 100);
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Document Information */}
                      <div className="mb-3 pb-3 border-b border-gray-100">
                        <div className="flex items-center space-x-2 mb-2">
                          <FileText className="w-4 h-4 text-orange-600" />
                          <h3 className="text-sm font-semibold text-gray-900">
                            Document Information
                          </h3>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                          <FormInput
                            form={form}
                            label="Master Invoice"
                            name={`entries.${index}.masterInvoice`}
                            type="text"
                            onBlur={(e) => {
                              const masterInvoiceValue = e.target.value;
                              if (masterInvoiceValue) {
                                form.setValue(
                                  `entries.${index}.invoice`,
                                  masterInvoiceValue
                                );
                              }
                            }}
                          />
                          <FormInput
                            form={form}
                            label="Invoice"
                            name={`entries.${index}.invoice`}
                            type="text"
                            isRequired
                          />
                          <FormInput
                            form={form}
                            label="BOL"
                            name={`entries.${index}.bol`}
                            type="text"
                            isRequired
                          />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                          <FormInput
                            form={form}
                            label="Received Date"
                            name={`entries.${index}.receivedDate`}
                            type="date"
                            isRequired
                          />
                          <FormInput
                            form={form}
                            label="Invoice Date"
                            name={`entries.${index}.invoiceDate`}
                            type="date"
                            isRequired
                          />
                          <FormInput
                            form={form}
                            label="Shipment Date"
                            name={`entries.${index}.shipmentDate`}
                            type="date"
                            isRequired
                          />
                        </div>
                      </div>

                      {/* Financial & Shipment Information */}
                      <div className="mb-4 pb-4 border-b border-gray-100">
                        <div className="flex items-center space-x-2 mb-3">
                          <DollarSign className="w-4 h-4 text-green-600" />
                          <h3 className="text-sm font-semibold text-gray-900">
                            Financial & Shipment
                          </h3>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                          <FormInput
                            className="mt-2"
                            form={form}
                            label="Invoice Total"
                            name={`entries.${index}.invoiceTotal`}
                            type="number"
                            isRequired
                          />
                          <SearchSelect
                            form={form}
                            name={`entries.${index}.currency`}
                            label="Currency"
                            placeholder="Search currency"
                            isRequired
                            options={[
                              { value: "USD", label: "USD" },
                              { value: "CAD", label: "CAD" },
                              { value: "EUR", label: "EUR" },
                            ]}
                          />
                          <FormInput
                            form={form}
                            className="mt-2"
                            label="Quantity Shipped"
                            name={`entries.${index}.qtyShipped`}
                            type="number"
                            isRequired
                          />
                          <FormInput
                            form={form}
                            className="mt-2"
                            label="Weight Unit"
                            name={`entries.${index}.weightUnitName`}
                            type="text"
                            isRequired
                          />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2">
                          <FormInput
                            form={form}
                            label="Savings"
                            name={`entries.${index}.savings`}
                            type="text"
                          />
                          <FormInput
                            form={form}
                            label="Invoice Type"
                            name={`entries.${index}.invoiceType`}
                            type="text"
                            isRequired
                          />
                          <FormInput
                            form={form}
                            label="Quantity Billed Text"
                            name={`entries.${index}.quantityBilledText`}
                            type="text"
                          />
                          <FormInput
                            form={form}
                            label="Invoice Status"
                            name={`entries.${index}.invoiceStatus`}
                            type="text"
                            isRequired
                          />
                        </div>
                      </div>

                      {/* Additional Information & Documents */}
                      <div className="mb-3">
                        <div className="flex items-center space-x-2 mb-2">
                          <Info className="w-4 h-4 text-gray-600" />
                          <h3 className="text-sm font-semibold text-gray-900">
                            Additional Information
                          </h3>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
                          <FormInput
                            form={form}
                            label="Manual Matching"
                            name={`entries.${index}.manualMatching`}
                            type="text"
                            isRequired
                          />
                          <FormInput
                            form={form}
                            label="Notes"
                            name={`entries.${index}.notes`}
                            type="text"
                          />
                          <FormInput
                            form={form}
                            label="Mistake"
                            name={`entries.${index}.mistake`}
                            type="text"
                          />
                          <div>
                            <FormCheckboxGroup
                              form={form}
                              label="Documents Available"
                              name={`entries.${index}.docAvailable`}
                              options={[
                                { label: "Invoice", value: "Invoice" },
                                { label: "BOL", value: "Bol" },
                                { label: "POD", value: "Pod" },
                                {
                                  label: "Packages List",
                                  value: "Packages List",
                                },
                              ]}
                              className="flex-row gap-2 text-xs"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Custom Fields Section */}
                      {(() => {
                        // Use entries from useWatch to ensure reactivity
                        const currentEntry = entries?.[index] as any;
                        const customFields = currentEntry?.customFields || [];

                        // Add refresh dependency to force re-render
                        const refreshKey = customFieldsRefresh;

                        return Array.isArray(customFields) &&
                          customFields.length > 0 ? (
                          <div
                            key={`custom-fields-${index}-${refreshKey}`}
                            className="pt-3 border-t border-gray-100"
                          >
                            <div className="flex items-center space-x-2 mb-2">
                              <Hash className="w-4 h-4 text-purple-600" />
                              <h3 className="text-sm font-semibold text-gray-900">
                                Custom Fields ({customFields.length})
                              </h3>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
                              {customFields.map((cf: any, cfIdx: number) => {
                                // Use the custom field data stored in the entry itself
                                const fieldType = cf.type || "TEXT";
                                const isAutoField = fieldType === "AUTO";
                                const autoOption = cf.autoOption;

                                // Determine input type based on field type and auto option
                                let inputType = "text";
                                if (
                                  fieldType === "DATE" ||
                                  (isAutoField && autoOption === "DATE")
                                ) {
                                  inputType = "date";
                                } else if (fieldType === "NUMBER") {
                                  inputType = "number";
                                }

                                // Create label with auto indicator
                                const fieldLabel = isAutoField
                                  ? `${cf.name} (Auto - ${autoOption})`
                                  : cf.name;

                                return (
                                  <FormInput
                                    key={cf.id}
                                    form={form}
                                    label={fieldLabel}
                                    name={`entries.${index}.customFields.${cfIdx}.value`}
                                    type={inputType}
                                    className="w-full"
                                    disable={isAutoField} // Disable auto fields to prevent manual editing
                                  />
                                );
                              })}
                            </div>
                          </div>
                        ) : null;
                      })()}

                      {/* Entry Actions - Moved to bottom */}
                      <div className="pt-3 border-t border-gray-100 mt-3">
                        <div className="flex items-center justify-end space-x-2">
                          {/* Filename Status Tooltip */}
                          <Tooltip>
                            <TooltipTrigger asChild tabIndex={-1}>
                              <div
                                className={`w-5 h-5 rounded-full flex items-center justify-center text-white font-bold text-xs cursor-help transition-colors duration-200 ${
                                  filenameValidation[index]
                                    ? "bg-green-500 hover:bg-green-600"
                                    : "bg-orange-500 hover:bg-orange-600"
                                }`}
                                tabIndex={-1}
                                role="button"
                                aria-label={`Entry ${
                                  index + 1
                                } filename status`}
                              >
                                !
                              </div>
                            </TooltipTrigger>
                            <TooltipContent
                              side="top"
                              align="center"
                              className="z-[9999]"
                            >
                              <div className="text-sm max-w-md">
                                <p className="font-medium mb-1">
                                  Entry #{index + 1} Filename
                                </p>
                                {filenameValidation[index] ? (
                                  <div>
                                    <p className="font-medium text-green-600 mb-2">
                                      Filename Generated
                                    </p>
                                    <p className="text-xs font-mono break-all bg-gray-100 p-2 rounded text-black">
                                      {generatedFilenames[index]}
                                    </p>
                                  </div>
                                ) : (
                                  <div>
                                    <p className="font-medium text-orange-600 mb-1">
                                      Please fill the form to generate filename
                                    </p>
                                    <p className="text-xs text-gray-600 mb-2">
                                      Missing fields:
                                    </p>
                                    <ul className="list-disc list-inside space-y-1">
                                      {missingFields[index]?.map(
                                        (field, fieldIndex) => (
                                          <li
                                            key={fieldIndex}
                                            className="text-xs"
                                          >
                                            {field}
                                          </li>
                                        )
                                      )}
                                    </ul>
                                  </div>
                                )}
                              </div>
                            </TooltipContent>
                          </Tooltip>

                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="h-7 w-7 p-0 hover:bg-red-50 hover:border-red-200"
                            onClick={() => removeEntry(index)}
                            disabled={fields.length <= 1}
                            tabIndex={-1}
                          >
                            <MinusCircle className="h-3 w-3 text-red-500" />
                          </Button>
                          {index === fields.length - 1 && (
                            <Tooltip>
                              <TooltipTrigger asChild tabIndex={-1}>
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  className="h-7 w-7 p-0 hover:bg-green-50 hover:border-green-200"
                                  onClick={addNewEntry}
                                  tabIndex={-1}
                                >
                                  <PlusCircle className="h-3 w-3 text-green-500" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent side="top" align="center">
                                <p className="text-xs">
                                  Add New Entry (Shift+Enter)
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Submit Section - Compact */}
                <div className="mt-3">
                  <div className="flex justify-center">
                    <Button
                      type="submit"
                      className="px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg text-sm"
                    >
                      Save
                    </Button>
                  </div>
                </div>
              </form>
            </Form>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
};

export default CreateTrackSheet;

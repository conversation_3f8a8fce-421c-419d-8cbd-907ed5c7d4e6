"use client";
import React, { useState, useEffect } from 'react';
import { useShortcuts } from './ShortcutManager';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { HelpCircle } from 'lucide-react';

const ShortcutHelp: React.FC = () => {
  const { shortcuts, userShortcuts, getActiveShortcut } = useShortcuts();
  const [isOpen, setIsOpen] = useState(false);

  // Listen for Shift + ? to open help
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.shiftKey && event.key === '?') {
        event.preventDefault();
        setIsOpen(true);
      }
      
      // Close on Escape
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen]);

  // Group shortcuts by category
  const groupedShortcuts = shortcuts.reduce((acc, shortcut) => {
    if (!acc[shortcut.category]) {
      acc[shortcut.category] = [];
    }
    acc[shortcut.category].push(shortcut);
    return acc;
  }, {} as Record<string, typeof shortcuts>);

  // Filter only enabled shortcuts
  const enabledShortcuts = shortcuts.filter(shortcut => {
    const userShortcut = userShortcuts.find(us => us.actionId === shortcut.id);
    return userShortcut?.enabled !== false;
  });

  const formatShortcut = (shortcut: string): string => {
    return shortcut
      .split('+')
      .map(key => key.trim())
      .map(key => {
        const keyMap: Record<string, string> = {
          'ctrl': 'Ctrl',
          'alt': 'Alt',
          'shift': 'Shift',
          'meta': 'Cmd',
          'enter': 'Enter',
          'escape': 'Esc',
          'space': 'Space',
        };
        return keyMap[key.toLowerCase()] || key.toUpperCase();
      })
      .join(' + ');
  };

  if (enabledShortcuts.length === 0) {
    return null;
  }

  return (
    <>
      {/* Help trigger hint */}
      <div className="fixed bottom-4 right-4 z-50">
        <div className="bg-gray-800 text-white px-3 py-2 rounded-lg shadow-lg text-sm flex items-center gap-2">
          <HelpCircle className="w-4 h-4" />
          Press <Badge variant="secondary" className="text-xs">Shift + ?</Badge> for shortcuts
        </div>
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <HelpCircle className="w-5 h-5" />
              Available Keyboard Shortcuts
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="text-sm text-gray-600">
              Here are all the keyboard shortcuts available on this page. You can customize these in the settings.
            </div>

            {Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => {
              const enabledCategoryShortcuts = categoryShortcuts.filter(shortcut => {
                const userShortcut = userShortcuts.find(us => us.actionId === shortcut.id);
                return userShortcut?.enabled !== false;
              });

              if (enabledCategoryShortcuts.length === 0) return null;

              return (
                <Card key={category}>
                  <CardHeader>
                    <CardTitle className="text-lg">{category}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {enabledCategoryShortcuts.map((shortcut) => {
                        const activeShortcut = getActiveShortcut(shortcut.id);
                        
                        return (
                          <div key={shortcut.id} className="flex items-center justify-between p-2 border rounded">
                            <div>
                              <div className="font-medium text-sm">{shortcut.name}</div>
                              <div className="text-xs text-gray-600">{shortcut.description}</div>
                            </div>
                            <Badge variant="outline" className="ml-2">
                              {formatShortcut(activeShortcut)}
                            </Badge>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              );
            })}

            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600">
                <strong>Tips:</strong>
                <ul className="mt-2 space-y-1 list-disc list-inside">
                  <li>Press <Badge variant="secondary" className="text-xs mx-1">Shift + ?</Badge> anytime to see this help</li>
                  <li>Press <Badge variant="secondary" className="text-xs mx-1">Esc</Badge> to close this dialog</li>
                  <li>Shortcuts work globally except when typing in input fields</li>
                  <li>You can customize all shortcuts in the settings menu</li>
                </ul>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ShortcutHelp;
